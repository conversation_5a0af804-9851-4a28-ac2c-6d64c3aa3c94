﻿
<style type="text/css">
	:root {
		--primary-color: #8dc63f;
		--background-color: #ffffff;
		--text-color: #333333;
		--border-radius: 8px;
		--shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		--transition: all 0.3s ease;
	}

	html, body {
		background-color: transparent !important;
		margin: 0;
		padding: 0;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
		color: var(--text-color);
		line-height: 1.6;
	}

	body {
		padding: 20px;
		font-size: 16px;
	}

	/* Modern table styles */
	table {
		width: 100%;
		max-width: 100%;
		margin-bottom: 1.5rem;
		border-collapse: separate;
		border-spacing: 0;
		background: var(--background-color);
		border-radius: var(--border-radius);
		box-shadow: var(--shadow);
	}

	td {
		padding: 12px 16px;
		transition: var(--transition);
	}

	tr:hover {
		background-color: rgba(0, 0, 0, 0.02);
	}

	/* Modern form elements */
	input, select {
		width: 100%;
		max-width: 300px;
		padding: 12px;
		margin: 5px 0;
		border: 1px solid #ddd;
		border-radius: var(--border-radius);
		box-sizing: border-box;
		transition: var(--transition);
		outline: none;
	}

	input:focus, select:focus {
		border-color: var(--primary-color);
		box-shadow: 0 0 0 2px rgba(141, 198, 63, 0.1);
	}

	input#calculate {
		background-color: var(--primary-color);
		color: white;
		font-weight: 600;
		border: none;
		padding: 14px 24px;
		cursor: pointer;
		transition: var(--transition);
	}

	input#calculate:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 8px rgba(141, 198, 63, 0.2);
	}

	input#calculate:active {
		transform: translateY(0);
	}

	/* Question icon and answers */
	.question {
		cursor: pointer;
		width: 24px;
		height: 24px;
		transition: var(--transition);
	}

	.question:hover {
		transform: scale(1.1);
	}

	[id^="answer-"] {
		display: none;
		background: #f8f9fa;
		padding: 20px;
		margin: 10px 0;
		border-radius: var(--border-radius);
		box-shadow: var(--shadow);
	}

	.answer p {
		font-size: 14px;
		margin: 0 5%;
		color: #666;
	}

	.table-head {
		background: #f1f3f5;
		border-radius: var(--border-radius) var(--border-radius) 0 0;
	}

	.table-head h3 {
		margin: 0;
		padding: 15px;
		color: var(--text-color);
	}

	/* Media Queries */
	@media screen and (max-width: 768px) {
		body {
			padding: 15px;
		}

		.answer p {
			margin: 0 2%;
		}

		td {
			padding: 10px 12px;
		}
	}

	@media screen and (max-width: 480px) {
		body {
			padding: 10px;
			font-size: 14px;
		}

		table {
			display: block;
			overflow-x: auto;
			-webkit-overflow-scrolling: touch;
		}

		input, select {
			font-size: 16px;
			padding: 10px;
		}

		.answer p {
			margin: 0;
			font-size: 13px;
		}

		td {
			padding: 8px 10px;
		}
	}
</style>

<!-- Add translations -->
<script type="text/javascript">
	const translations = {
    "nl": {
        "wandhangende": "Gegevens wandhangende of vrijstaande convector",
        "type": "Type",
        "standaard": "standaard",
        "twin": "twin",
        "diepteKast": "diepte kast",
        "hoogteKast": "hoogte kast",
        "breedteKast": "breedte kast",
        "werktemperatuur": "werktemperatuur",
        "ingaandeWatertemperatuur": "Ingaande watertemperatuur",
        "uitgaandeWatertemperatuur": "Uitgaande watertemperatuur",
        "ruimtetemperatuur": "Ruimtetemperatuur",
        "bereken": "Bereken",
        "vermogenZonderCB": "Warmteafgifte radiator zonder ClimateBooster bij gewenste temperatuur",
        "vermogenMetCB": "Warmteafgifte radiator met ClimateBooster bij gewenste temperatuur",
        "nominalePrestatie": "Nominale prestatie radiator (75/65/20)",
        "watt": "Watt",
        "d100": "100",
        "d150": "150",
        "d200": "200",
        "d250": "250",
        "h200": "200",
        "h300": "300",
        "h350": "350",
        "h400": "400",
        "h500": "500",
        "h600": "600",
        "h650": "650",
        "h800": "800",
        "h950": "950",
        "answer1Comment1": "Convectoren worden veelal geleverd in een 'standaard' of een 'twin' model. Bij een twin model wordt een dubbele warmtewisselaar toegepast. Een twin model biedt meer warmteafgifte in dezelfde omkasting.",
        "answer1Comment2": "Fabrikant Jaga duidt modellen met een enkele warmtewisselaar aan met type 10, 15 en 20. Twin modellen met type 11, 16 of 21",
        "answer1Comment3": "Bij fabrikant Betherma worden de modelen met een dubbele warmtewisselaar aangeduid met de toevoeging 'twin' in het typenummer.",
        "answer3Comment1": "Vul hier de gewenste ingaande watertemperatuur in.",
        "answer4Comment1": "De uitgaande watertemperatuur ligt gemiddeld 5 tot 10 graden lager dan de ingaande temperatuur. Weet u de uitgaande temperatuur niet, dan raden wij u aan een 10 graden lagere temperauur dan de ingaande temperatuur in te vullen.",
        "answer5Comment1": "Vul hier de gewenste ruimtetemperatuur in.",
        "answer7Comment1": "Dit is de warmteafgifte van de radiator zonder ClimateBooster bij de door u opgegeven temperaturen.",
        "answer8Comment1": "Dit is de warmteafgifte van de radiator met ClimateBooster bij de door u opgegeven temperaturen.",
        "answer9Comment1": "Dit is de warmteafgifte van de radiator bij een watertemperatuur van 75°C aanvoer, 65°C retour en een ruimtetemperatuur van 20°C.",
		"warmteafgifteConvector": "Warmteafgifte convector bij 75/65/20",
        "warmteafgifteConvectorZonderCB": "Warmteafgifte convector zonder ClimateBooster bij gewenste temperatuur",
        "warmteafgifteConvectorMetCB": "Warmteafgifte convector met ClimateBooster bij gewenste temperatuur"
    },
    "en": {
        "wandhangende": "Wall-mounted or free-standing convector details",
        "type": "Type",
        "standaard": "standard",
        "twin": "twin",
        "diepteKast": "cabinet depth",
        "hoogteKast": "cabinet height",
        "breedteKast": "cabinet width",
        "werktemperatuur": "operating temperature",
        "ingaandeWatertemperatuur": "Incoming water temperature",
        "uitgaandeWatertemperatuur": "Outgoing water temperature",
        "ruimtetemperatuur": "Room temperature",
        "bereken": "Calculate",
        "vermogenZonderCB": "Heat output radiator without ClimateBooster at desired temperature",
        "vermogenMetCB": "Heat output radiator with ClimateBooster at desired temperature",
        "nominalePrestatie": "Nominal radiator performance (75/65/20)",
        "watt": "Watt",
        // Numbers don't need translation
        "answer1Comment1": "Convectors are usually supplied in a 'standard' or 'twin' model. A twin model uses a double heat exchanger. A twin model provides more heat output in the same casing.",
        "answer1Comment2": "Manufacturer Jaga designates models with a single heat exchanger as type 10, 15 and 20. Twin models with type 11, 16 or 21",
        "answer1Comment3": "For manufacturer Betherma, models with a double heat exchanger are indicated with the addition of 'twin' in the type number.",
        "answer3Comment1": "Enter the desired incoming water temperature here.",
        "answer4Comment1": "The outgoing water temperature is on average 5 to 10 degrees lower than the incoming temperature. If you don't know the outgoing temperature, we recommend entering a temperature 10 degrees lower than the incoming temperature.",
        "answer5Comment1": "Enter the desired room temperature here.",
        "answer7Comment1": "This is the heat output of the radiator without ClimateBooster at your specified temperatures.",
        "answer8Comment1": "This is the heat output of the radiator with ClimateBooster at your specified temperatures.",
        "answer9Comment1": "This is the heat output of the radiator at a water temperature of 75°C supply, 65°C return and a room temperature of 20°C.",
		"warmteafgifteConvector": "Heat output convector at 75/65/20",
        "warmteafgifteConvectorZonderCB": "Heat output convector without ClimateBooster at desired temperature",
        "warmteafgifteConvectorMetCB": "Heat output convector with ClimateBooster at desired temperature"
    },
    "de": {
        "wandhangende": "Daten des wandhängenden oder freistehenden Konvektors",
        "type": "Typ",
        "standaard": "standard",
        "twin": "twin",
        "diepteKast": "Gehäusetiefe",
        "hoogteKast": "Gehäusehöhe",
        "breedteKast": "Gehäusebreite",
        "werktemperatuur": "Betriebstemperatur",
        "ingaandeWatertemperatuur": "Vorlauftemperatur",
        "uitgaandeWatertemperatuur": "Rücklauftemperatur",
        "ruimtetemperatuur": "Raumtemperatur",
        "bereken": "Berechnen",
        "vermogenZonderCB": "Wärmeleistung Heizkörper ohne ClimateBooster bei gewünschter Temperatur",
        "vermogenMetCB": "Wärmeleistung Heizkörper mit ClimateBooster bei gewünschter Temperatur",
        "nominalePrestatie": "Nominale Heizkörperleistung (75/65/20)",
        "watt": "Watt",
        // Numbers don't need translation
        "answer1Comment1": "Konvektoren werden üblicherweise in einer 'Standard'- oder 'Twin'-Ausführung geliefert. Ein Twin-Modell verwendet einen doppelten Wärmetauscher. Ein Twin-Modell bietet mehr Wärmeleistung im gleichen Gehäuse.",
        "answer1Comment2": "Hersteller Jaga bezeichnet Modelle mit einem einzelnen Wärmetauscher als Typ 10, 15 und 20. Twin-Modelle mit Typ 11, 16 oder 21",
        "answer1Comment3": "Bei Hersteller Betherma werden Modelle mit doppeltem Wärmetauscher durch den Zusatz 'twin' in der Typenbezeichnung gekennzeichnet.",
        "answer3Comment1": "Geben Sie hier die gewünschte Vorlauftemperatur ein.",
        "answer4Comment1": "Die Rücklauftemperatur liegt durchschnittlich 5 bis 10 Grad unter der Vorlauftemperatur. Wenn Sie die Rücklauftemperatur nicht kennen, empfehlen wir eine um 10 Grad niedrigere Temperatur als die Vorlauftemperatur einzugeben.",
        "answer5Comment1": "Geben Sie hier die gewünschte Raumtemperatur ein.",
        "answer7Comment1": "Dies ist die Wärmeleistung des Heizkörpers ohne ClimateBooster bei den von Ihnen angegebenen Temperaturen.",
        "answer8Comment1": "Dies ist die Wärmeleistung des Heizkörpers mit ClimateBooster bei den von Ihnen angegebenen Temperaturen.",
        "answer9Comment1": "Dies ist die Wärmeleistung des Heizkörpers bei einer Wassertemperatur von 75°C Vorlauf, 65°C Rücklauf und einer Raumtemperatur von 20°C.",
		"warmteafgifteConvector": "Wärmeleistung Konvektor bei 75/65/20",
		"warmteafgifteConvectorZonderCB": "Wärmeleistung Konvektor ohne ClimateBooster bei gewünschter Temperatur",
		"warmteafgifteConvectorMetCB": "Wärmeleistung Konvektor mit ClimateBooster bei gewünschter Temperatur"
    }
};


	function getText(key, lang = 'nl') {
		return translations[lang][key] || key;
	}

	function updateLanguage(lang) {
		document.querySelectorAll('[data-translate]').forEach(element => {
			const key = element.getAttribute('data-translate');
			element.textContent = getText(key, lang);
		});

		document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
			const key = element.getAttribute('data-translate-placeholder');
			element.placeholder = getText(key, lang);
		});

		document.querySelectorAll('option[data-translate]').forEach(element => {
			const key = element.getAttribute('data-translate');
			element.textContent = getText(key, lang);
		});

		document.querySelectorAll('[data-translate-value]').forEach(element => {
			const key = element.getAttribute('data-translate-value');
			element.value = getText(key, lang);
		});
	}

	// Initialize language on page load
	document.addEventListener('DOMContentLoaded', function() {
		updateLanguage('nl');
	});
</script>


    <script type="text/javascript" language="javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>

    <script language="JavaScript" type="text/javascript">
function getRadioVal(form, name) {
    var val;
    // get list of radio buttons with specified name
    var radios = form.elements[name];
    
    // loop through list of radio buttons
    for (var i=0, len=radios.length; i<len; i++) {
        if ( radios[i].checked ) { // radio checked?
            val = radios[i].value; // if so, hold its value in val
            break; // and break out of for loop
        }
    }
    return val; // return value of checked radio or undefined if none checked
} 

function reken (form)
{
var typeconvector = getRadioVal( document.getElementById('form'), 'typeconvector' );
var diepte = form.diepte.value;
var hoogte = form.hoogte.value;
var breedte = form.breedte.value-100;
var Twaterin = form.Twaterin.value;
var Twaterout = form.Twaterout.value;
var Troom = form.Troom.value;
var deltaTwater = Twaterin/2+Twaterout/2;
var deltaT = deltaTwater-Troom;
var Qnominal = 0;
switch (typeconvector) {
    case 'enkel':
		switch (diepte) {
			case "d100":
				switch (hoogte) {
					case "h200":
		        		Qnominal = 0.6791*breedte-18;
						break;
					case "h300":
		        		Qnominal = 0.852*breedte;
						break;
					case "h350":
		        		Qnominal = 0.8979*breedte;
						break;
					case "h400":
		        		Qnominal = 0.982*breedte;
						break;
					case "h500":
				        Qnominal = 1.0928*breedte-13;
						break;
					case "h600":
				        Qnominal = 1.166*breedte;
						break;
					case "h650":
				        Qnominal = 1.2108*breedte;
						break;
					case "h800":
				        Qnominal = 1.274*breedte;
						break;
					case "h950":
				        Qnominal = 1.3929*breedte;
						break;
				}
				break;
			case "d150":
				switch (hoogte) {
					case "h200":
		        		Qnominal = 0.9385*breedte+46;
						break;
					case "h300":
		        		Qnominal = 1.223*breedte;
						break;
					case "h350":
		        		Qnominal = 1.47*breedte;
						break;
					case "h400":
		        		Qnominal = 1.412*breedte;
						break;
					case "h500":
				        Qnominal = 1.57*breedte;
						break;
					case "h600":
				        Qnominal = 1.7049*breedte;
						break;
					case "h650":
				        Qnominal = 1.922*breedte;
						break;
					case "h800":
				        Qnominal = 1.924*breedte;
						break;
					case "h950":
				        Qnominal = 2.1455*breedte;
						break;
				}
				break;
			case "d200":
				switch (hoogte) {
					case "h200":
		        		Qnominal = 1.3409*breedte;
						break;
					case "h300":
		        		Qnominal = 1.6369*breedte;
						break;
					case "h350":
		        		Qnominal = 2.06*breedte;
						break;
					case "h400":
		        		Qnominal = 1.868*breedte;
						break;
					case "h500":
				        Qnominal = 2.053*breedte;
						break;
					case "h600":
				        Qnominal = 2.203*breedte;
						break;
					case "h650":
				        Qnominal = 2.6848*breedte;
						break;
					case "h800":
				        Qnominal = 2.4269*breedte;
						break;
					case "h950":
				        Qnominal = 2.9922*breedte;
						break;
				}
				break;
			case "d250":
				switch (hoogte) {
					case "h200":
		        		Qnominal = 1.6269*breedte;
						break;
					case "h300":
		        		Qnominal = 2.024*breedte;
						break;
					case "h350":
		        		Qnominal = 2.1406*breedte;
						break;
					case "h400":
		        		Qnominal = 2.324*breedte;
						break;
					case "h500":
				        Qnominal = 2.588*breedte;
						break;
					case "h600":
				        Qnominal = 2.7269*breedte;
						break;
					case "h650":
				        Qnominal = 2.8137*breedte;
						break;
					case "h800":
				        Qnominal = 2.9708*breedte;
						break;
					case "h950":
				        Qnominal = 3.3273*breedte;
						break;
				}	
				break;
	        break;
		}
		break;
    case 'twin':
		switch (diepte) {
			case "d100":
				switch (hoogte) {
					case "h200":
		        		Qnominal = 1.0664*breedte;
						break;
					case "h300":
		        		Qnominal = 1.1595*breedte;
						break;
					case "h350":
		        		Qnominal = 1.196*breedte;
						break;
					case "h400":
		        		Qnominal = 1.198*breedte;
						break;
					case "h500":
				        Qnominal = 1.4084*breedte-17;
						break;
					case "h600":
				        Qnominal = 1.492*breedte;
						break;
					case "h650":
				        Qnominal = 1.5428*breedte;
						break;
					case "h800":
				        Qnominal = 1.6309*breedte;
						break;
					case "h950":
				        Qnominal = 1.7955*breedte;
						break;
				}
				break;
			case "d150":
				switch (hoogte) {
					case "h200":
		        		Qnominal = 1.1975*breedte;
						break;
					case "h300":
		        		Qnominal = 1.3379*breedte-10;
						break;
					case "h350":
		        		Qnominal = 1.5928*breedte;
						break;
					case "h400":
		        		Qnominal = 1.539*breedte;
						break;
					case "h500":
				        Qnominal = 1.7269*breedte;
						break;
					case "h600":
				        Qnominal = 1.91*breedte;
						break;
					case "h650":
				        Qnominal = 2.1728*breedte;
						break;
					case "h800":
				        Qnominal = 2.213*breedte;
						break;
					case "h950":
				        Qnominal = 2.6771*breedte;
						break;
				}
				break;
			case "d200":
				switch (hoogte) {
					
					case "h200":
		        		Qnominal = 1.3408*breedte;
						break;
					case "h300":
		        		Qnominal = 1.6369*breedte;
						break;
					case "h350":
		        		Qnominal = 2.1141*breedte;
						break;
					case "h400":
		        		Qnominal = 1.868*breedte;
						break;
					case "h500":
				        Qnominal = 2.053*breedte;
						break;
					case "h600":
				        Qnominal = 2.203*breedte;
						break;
					case "h650":
				        Qnominal = 3.03*breedte;
						break;
					case "h800":
				        Qnominal = 3.4269*breedte;
						break;
					case "h950":
				        Qnominal = 3.92*breedte;
						break;
				}
				break;
			case "d250":
				switch (hoogte) {
					case "h200":
		        		Qnominal = 1.6978*breedte;
						break;
					case "h300":
		        		Qnominal = 2.0848*breedte;
						break;
					case "h350":
		        		Qnominal = 2.2702*breedte;
						break;
					case "h400":
		        		Qnominal = 2.4169*breedte;
						break;
					case "h500":
				        Qnominal = 2.769*breedte;
						break;
					case "h600":
				        Qnominal = 3.0269*breedte;
						break;
					case "h650":
				        Qnominal = 3.1305*breedte;
						break;
					case "h800":
				        Qnominal = 3.446*breedte;
						break;
					case "h950":
				        Qnominal = 3.8121*breedte;
						break;
					}	
		        break;
			}
        break;
		break;
}
//form.Qnominal.value = Qnominal;
//var Qnominal = form.Qnominal.value;
var fractie= Math.pow(deltaT/50,1.36);
var total= fractie*Qnominal;

var BST1 = Twaterin-Troom;
var BST2 = -0.0095*BST1;
var BST3 = 1.771+ BST2;
var totalwithCB = total* BST3;
form.Q_output.value = Math.round(total);
form.Q_outputCB.value = Math.round(totalwithCB);
form.Qnominal.value = Math.round(Qnominal);
}</script>
    <script language="JavaScript" type="text/javascript">
    $(document).ready(function(){
    $("#question-1").click(function(){
        $("#answer-1").toggle(1000);
    });
	$("#question-2").click(function(){
        $("#answer-2").toggle(1000);
    });
	$("#question-3").click(function(){
        $("#answer-3").toggle(1000);
    });
	$("#question-4").click(function(){
        $("#answer-4").toggle(1000);
    });
	$("#question-5").click(function(){
        $("#answer-5").toggle(1000);
    });
	$("#question-6").click(function(){
        $("#answer-6").toggle(1000);
    });
	$("#question-7").click(function(){
        $("#answer-7").toggle(1000);
    });
	$("#question-8").click(function(){
        $("#answer-8").toggle(1000);
    });
	$("#question-9").click(function(){
        $("#answer-9").toggle(1000);
    });
	$("#question-10").click(function(){
        $("#answer-10").toggle(1000);
    });
	$("#question-11").click(function(){
        $("#answer-11").toggle(1000);
    });
});</script>

<!-- Add language selector -->
<div style="text-align: right; margin-bottom: 20px;">
	<select id="languageSelector" onchange="updateLanguage(this.value)" style="padding: 8px; border-radius: 4px; border: 1px solid #ccc; max-width: 150px;">
		<option value="nl">Nederlands</option>
		<option value="en">English</option>
		<option value="de">Deutsch</option>
	</select>
</div>

<form name="form" class="form" id="form">
<table style="width:100%; background:#fff;">
	<colgroup> 
		<col style="width: 25% !important;"> 
		<col style="width: 5% !important;"> 
		<col style="width: 35% !important;"> 
		<col style="width: 25% !important;"> 
	</colgroup>
	<tbody><tr>
		<td colspan="4" class="table-head"><h3 data-translate="wandhangende">Gegevens wandhangende of vrijstaande convector</h3></td>
	</tr>
    <tr>
		<td data-translate="type">Type
       </td>
		<td>
        	<img id="question-1" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp">
        </td>
		<td>
        <div style="width:50%; float:left; text-align:center;">
        	<img src="https://static.wixstatic.com/media/159d65_fac526fe566140d6b27669fae097f789~mv2.jpg/v1/fill/w_305,h_320,al_c,q_80/enkel.webp" style="width:100%;">
	        <p><input type="radio" value="enkel" name="typeconvector" checked="checked"><span data-translate="standaard">standaard</span></p>
         </div>
        <div style="width:50%; float:left; text-align:center">
            <img src="https://static.wixstatic.com/media/159d65_13147ca3f81c45e49c30da156f9bd2f1~mv2.jpg/v1/fill/w_305,h_320,al_c,q_80/twin.webp" style="width:100%;">
            <p><input type="radio" value="twin" name="typeconvector"><span data-translate="twin">twin</span></p>
        </div>
        </td>
		<td>&nbsp;</td>
	</tr>
    <tr id="answer-1" class="answer">
    	<td colspan="4">
		   <p data-translate="answer1Comment1">Convectoren worden veelal geleverd in een 'standaard' of een 'twin' model. Bij een twin model wordt een dubbele warmtewisselaar toegepast. Een twin model biedt meer warmteafgifte in dezelfde omkasting.</p>
           <p data-translate="answer1Comment2">Fabrikant Jaga duidt modellen met een enkele warmtewisselaar aan met type 10, 15 en 20. Twin modellen met type 11, 16 of 21</p>
           <p data-translate="answer1Comment3">Bij fabrikant Betherma worden de modelen met een dubbele warmtewisselaar aangeduid met de toevoeging 'twin' in het typenummer.</p>
	    </td>
    </tr>
    <tr>
		<td data-translate="diepteKast">diepte kast</td>
		<td>&nbsp;</td>
		<td>
		    <select name="diepte">
				<option value="d100" data-translate="d100" selected="selected">100</option>
				<option value="d150" data-translate="d150">150</option>
    	        <option value="d200" data-translate="d200">200</option>
				<option value="d250" data-translate="d250">250</option>
			</select> 
        </td>
		<td>mm</td>
	</tr>    
    <tr>
		<td data-translate="hoogteKast">hoogte kast</td>
		<td>&nbsp;</td>
		<td>
		    <select name="hoogte">
				<option value="h200" data-translate="h200">200</option>
                <option value="h300" data-translate="h300">300</option>
                <option value="h350" data-translate="h350">350</option>
				<option value="h400" data-translate="h400">400</option>
    	        <option value="h500" data-translate="h500" selected="selected">500</option>
				<option value="h600" data-translate="h600">600</option>
        	   	<option value="h650" data-translate="h650">650</option>
                 <option value="h800" data-translate="h800">800</option>
                <option value="h950" data-translate="h950">950</option>
			</select> 
        </td>
		<td>mm</td>
	</tr>    
	<tr>
		<td data-translate="breedteKast">breedte kast</td>
		<td>&nbsp;</td>
		<td><input size="" name="breedte" value="1000"></td>
		<td>mm</td>
	</tr>
    <tr>
		<td colspan="4" class="table-head"><h3 data-translate="werktemperatuur">werktemperatuur</h3></td>
	</tr>
	<tr>
		<td data-translate="ingaandeWatertemperatuur">Ingaande watertemperatuur</td>
		<td><img id="question-3" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp"></td>
		<td><input size="5" name="Twaterin" value="75"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-3" class="answer">
    	<td colspan="4">
		    <p data-translate="answer3Comment1">Vul hier de gewenste ingaande watertemperatuur in.</p>
	    </td>
    </tr>
	<tr>
		<td data-translate="uitgaandeWatertemperatuur">Uitgaande watertemperatuur</td>
		<td><img id="question-4" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp"></td>
		<td><input size="5" name="Twaterout" value="65"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-4" class="answer">
    	<td colspan="4">
		    <p data-translate="answer4Comment1">De uitgaande watertemperatuur ligt gemiddeld 5 tot 10 graden lager dan de ingaande temperatuur. Weet u de uitgaande temperatuur niet, dan raden wij u aan een 10 graden lagere temperauur dan de ingaande temperatuur in te vullen.</p>
	    </td>
    </tr>
	<tr>
		<td data-translate="ruimtetemperatuur">Ruimtetemperatuur</td>
		<td><img id="question-5" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp"></td>
		<td><input size="5" name="Troom" value="20"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-5" class="answer">
    	<td colspan="4">
		    <p data-translate="answer5Comment1">Vul hier de gewenste ruimtetemperatuur in.</p>
	    </td>
    </tr>
	<tr>
		<td colspan="4" class="table-head"><h3 data-translate="resultaat">Resultaat</h3></td>
	</tr>

	<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>
			<input onClick="reken(this.form)" value="Bereken" id="calculate" type="button">
		</td>
		<td>&nbsp;</td>
	</tr>
		<tr>
		<td data-translate="warmteafgifteConvector">Warmteafgifte convector bij 75/65/20
       </td>
		<td>
        	<img id="question-6" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp">
        </td>
		<td><input size="5" name="Qnominal" value=""></td>
		<td>Watt</td>
	</tr>
    <tr id="answer-6" class="answer">
    	<td colspan="4">
        	<p data-translate="geschatteWarmteafgifte">Geschatte warmteafgifte van de wandhangende en vrijstaande convector </p>
        </td>
    </tr>
	<tr>
		<td data-translate="warmteafgifteConvectorZonderCB">Warmteafgifte convector zonder ClimateBooster bij gewenste temperatuur</td>
		<td><img src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp" alt="" class="question" id="question-7"></td>
		<td><input size="5" name="Q_output" value=""></td>
		<td>Watt</td>
	</tr>
    <tr id="answer-7" class="answer">
    	<td colspan="4">
        	<p data-translate="geschatteWarmteafgifteZonderCB">Geschatte warmteafgifte van de convector bij de gewenste watertemperatuur zonder plaatsing van een ClimateBooster. </p>
        </td>
    </tr>
	<tr>
		<td><b data-translate="warmteafgifteConvectorMetCB">Warmteafgifte convector met ClimateBooster bij gewenste temperatuur </b></td>
		<td><img src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp" alt="" class="question" id="question-8"></td>
		<td><strong>
		  <input size="5" name="Q_outputCB" value="">
		</strong></td>
		<td><strong>Watt</strong></td>
	</tr>
    <tr id="answer-8" class="answer">
    	<td colspan="4">
        	<p data-translate="geschatteWarmteafgifteMetCB">Geschatte warmteafgifte van de ingegeven wandhangende en vrijstaande convector voorzien van een ClimateBooster.</p>
        </td>
    </tr>
</tbody></table>
</form>
