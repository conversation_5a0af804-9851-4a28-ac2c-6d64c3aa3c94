﻿
<style type="text/css">
	:root {
		--primary-color: #8dc63f;
		--background-color: #ffffff;
		--text-color: #333333;
		--border-radius: 8px;
		--shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		--transition: all 0.3s ease;
	}

	html, body {
		background-color: transparent !important;
		margin: 0;
		padding: 0;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
		color: var(--text-color);
		line-height: 1.6;
	}

	body {
		padding: 20px;
		font-size: 16px;
	}

	/* Modern table styles */
	table {
		width: 100%;
		max-width: 100%;
		margin-bottom: 1.5rem;
		border-collapse: separate;
		border-spacing: 0;
		background: var(--background-color);
		border-radius: var(--border-radius);
		box-shadow: var(--shadow);
	}

	td {
		padding: 12px 16px;
		transition: var(--transition);
	}

	tr:hover {
		background-color: rgba(0, 0, 0, 0.02);
	}

	/* Modern form elements */
	input, select {
		width: 100%;
		max-width: 300px;
		padding: 12px;
		margin: 5px 0;
		border: 1px solid #ddd;
		border-radius: var(--border-radius);
		box-sizing: border-box;
		transition: var(--transition);
		outline: none;
	}

	input:focus, select:focus {
		border-color: var(--primary-color);
		box-shadow: 0 0 0 2px rgba(141, 198, 63, 0.1);
	}

	input#calculate {
		background-color: var(--primary-color);
		color: white;
		font-weight: 600;
		border: none;
		padding: 14px 24px;
		cursor: pointer;
		transition: var(--transition);
	}

	input#calculate:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 8px rgba(141, 198, 63, 0.2);
	}

	input#calculate:active {
		transform: translateY(0);
	}

	/* Question icon and answers */
	.question {
		cursor: pointer;
		width: 24px;
		height: 24px;
		transition: var(--transition);
	}

	.question:hover {
		transform: scale(1.1);
	}

	[id^="answer-"] {
		display: none;
		background: #f8f9fa;
		padding: 20px;
		margin: 10px 0;
		border-radius: var(--border-radius);
		box-shadow: var(--shadow);
	}

	.answer p {
		font-size: 14px;
		margin: 0 5%;
		color: #666;
	}

	.table-head {
		background: #f1f3f5;
		border-radius: var(--border-radius) var(--border-radius) 0 0;
	}

	.table-head h3 {
		margin: 0;
		padding: 15px;
		color: var(--text-color);
	}

	/* Media Queries */
	@media screen and (max-width: 768px) {
		body {
			padding: 15px;
		}

		.answer p {
			margin: 0 2%;
		}

		td {
			padding: 10px 12px;
		}
	}

	@media screen and (max-width: 480px) {
		body {
			padding: 10px;
			font-size: 14px;
		}

		table {
			display: block;
			overflow-x: auto;
			-webkit-overflow-scrolling: touch;
		}

		input, select {
			font-size: 16px;
			padding: 10px;
		}

		.answer p {
			margin: 0;
			font-size: 13px;
		}

		td {
			padding: 8px 10px;
		}
	}
</style>

<!-- Add translations -->
<script type="text/javascript">
	const translations = {
		nl: {
			"wandhangende": "Gegevens wandhangende of vrijstaande convector",
			"type": "Type",
			"diepte": "Diepte",
			"breedte": "Breedte",
			"watertemperatuurAanvoer": "Watertemperatuur aanvoer",
			"watertemperatuurRetour": "Watertemperatuur retour",
			"ruimtetemperatuur": "Ruimtetemperatuur",
			"werktemperatuur": "Werktemperatuur",
			"bereken": "Bereken",
			"resultaat": "Resultaat",
			"vermogenZonderCB": "Vermogen zonder ClimateBooster",
			"vermogenMetCB": "Vermogen met ClimateBooster",
			"nominalVermogen": "Nominaal vermogen",
			"standaard": "Standaard",
			"enkel": "Enkel",
			"twin": "Twin",
			"extraVermogen": "Extra vermogen",
			"selecteerType": "Selecteer type",
			"selecteerDiepte": "Selecteer diepte",
			"voerBreedteIn": "Voer breedte in",
			"d100": "100mm",
			"d150": "150mm",
			"d200": "200mm",
			"d250": "250mm",
			"diepteKast": "Diepte kast",
			"breedteKast": "Breedte kast",
			"ingaandeWatertemperatuur": "Ingaande watertemperatuur",
			"uitgaandeWatertemperatuur": "Uitgaande watertemperatuur",
			"koudeAfgifteConvector": "Koudeafgifte convector",
			"koudeAfgifteConvectorMetClimateBooster": "Koudeafgifte convector met ClimateBooster",
			"koudeAfgifteConvectorMetClimateBoosterError": "Ventilatoren ClimateBooster niet actief. Ventilatoren wordt geactiveerd wanneer het temperatuurverschil tussen lucht en het ingaande water groter is dan 3 graden.",
			"koudeAfgifteConvectorMetClimateBoosterHelp": "Geschatte koudeafgifte van de ingegeven wandhangende en vrijstaande convector welke voorzien is van een Convector Pro.",
			"answer1Comment1": "Convectoren worden veelal geleverd in een standaard- en een 'twin'-model. Bij een twin-model wordt een dubbele warmtewisselaar toegepast. Een twin-model biedt meer afgifte in dezelfde omkasting.",
			"answer1Comment2": "Farbikant Jaga duidt modellen met een enkele warmtewisselaar aan met type 10, 15 en 20. Twin modellen met type 11, 16 of 21",
			"answer1Comment3": "Farbikant Betherma worden de modelen met dubbele warmtewisselaar aangeduid met toevoeging twin in het typenummer.",
			"answer3Comment1": "Vul hier de gewenste ingaande watertemperatuur in.",
			"answer4Comment1": "De uitgaande watertemperatuur ligt gemiddeld 5 tot 10 graden lager dan de ingaande watertemperatuur. Weet u de uitgaande temperatuur niet, dan raden wij u aan een 10 graden lagere temperatuur dan de ingaande temperatuur in te vullen.",
			"answer5Comment1": "Vul hier de gewenste ruimtetemperatuur in.",
			"answer6Comment1": "Geschatte koudeafgifte van de wandhangende en vrijstaande convector zonder plaatsing van een Convector Pro."
		},
		en: {
			"diepteKast": "Cabinet depth",
			"breedteKast": "Cabinet width",
			"ingaandeWatertemperatuur": "Incoming water temperature",
			"uitgaandeWatertemperatuur": "Outgoing water temperature",
			"koudeAfgifteConvector": "Convector cooling output",
			"wandhangende": "Wall-mounted or free-standing convector data",
			"type": "Type",
			"diepte": "Depth",
			"breedte": "Width",
			"watertemperatuurAanvoer": "Supply water temperature",
			"watertemperatuurRetour": "Return water temperature",
			"ruimtetemperatuur": "Room temperature",
			"werktemperatuur": "Operating temperature",
			"bereken": "Calculate",
			"resultaat": "Result",
			"vermogenZonderCB": "Power without ClimateBooster",
			"vermogenMetCB": "Power with ClimateBooster",
			"nominalVermogen": "Nominal power",
			"standaard": "Standard",
			"enkel": "Single",
			"twin": "Twin",
			"extraVermogen": "Extra power",
			"selecteerType": "Select type",
			"selecteerDiepte": "Select depth",
			"voerBreedteIn": "Enter width",
			"d100": "100mm",
			"d150": "150mm",
			"d200": "200mm",
			"d250": "250mm",
			"diepteKast": "Cabinet depth",
			"breedteKast": "Cabinet width",
			"ingaandeWatertemperatuur": "Incoming water temperature",
			"uitgaandeWatertemperatuur": "Outgoing water temperature",
			"koudeAfgifteConvector": "Convector cooling output",
			"koudeAfgifteConvectorMetClimateBooster": "Convector cooling output with ClimateBooster",
			"koudeAfgifteConvectorMetClimateBoosterError": "ClimateBooster fans not active. Fans will activate when the temperature difference between air and incoming water is greater than 3 degrees.",
			"koudeAfgifteConvectorMetClimateBoosterHelp": "Estimated cooling output of the given wall-mounted and free-standing convector with a Convector Pro.",
			"answer1Comment1": "Convectors are usually supplied in a standard and 'twin' model. A twin model uses a double heat exchanger. A twin model provides more output in the same casing.",
			"answer1Comment2": "Manufacturer Jaga designates models with a single heat exchanger as type 10, 15 and 20. Twin models with type 11, 16 or 21",
			"answer1Comment3": "Manufacturer Betherma models with double heat exchanger are indicated with twin addition in the type number.",
			"answer3Comment1": "Enter the desired incoming water temperature here.",
			"answer4Comment1": "The outgoing water temperature is on average 5 to 10 degrees lower than the incoming temperature. If you don't know the outgoing temperature, we recommend entering a temperature 10 degrees lower than the incoming temperature.",
			"answer5Comment1": "Enter the desired room temperature here.",
			"answer6Comment1": "Estimated cooling output of the wall-mounted and free-standing convector without installation of a Convector Pro."
		},
		de: {
			"diepteKast": "Gehäusetiefe",
			"breedteKast": "Gehäusebreite",
			"ingaandeWatertemperatuur": "Vorlauftemperatur",
			"uitgaandeWatertemperatuur": "Rücklauftemperatur",
			"koudeAfgifteConvector": "Kühlleistung Konvektor",
			"wandhangende": "Daten für Wand- oder freistehenden Konvektor",
			"type": "Typ",
			"diepte": "Tiefe",
			"breedte": "Breite",
			"watertemperatuurAanvoer": "Wasservorlauftemperatur",
			"watertemperatuurRetour": "Wasserrücklauftemperatur",
			"ruimtetemperatuur": "Raumtemperatur",
			"werktemperatuur": "Betriebstemperatur",
			"bereken": "Berechnen",
			"resultaat": "Ergebnis",
			"vermogenZonderCB": "Leistung ohne ClimateBooster",
			"vermogenMetCB": "Leistung mit ClimateBooster",
			"nominalVermogen": "Nennleistung",
			"standaard": "Standard",
			"enkel": "Einzel",
			"twin": "Twin",
			"extraVermogen": "Zusätzliche Leistung",
			"selecteerType": "Typ auswählen",
			"selecteerDiepte": "Tiefe auswählen",
			"voerBreedteIn": "Breite eingeben",
			"d100": "100mm",
			"d150": "150mm",
			"d200": "200mm",
			"d250": "250mm",
			"diepteKast": "Gehäusetiefe",
			"breedteKast": "Gehäusebreite",
			"ingaandeWatertemperatuur": "Vorlauftemperatur",
			"uitgaandeWatertemperatuur": "Rücklauftemperatur",
			"koudeAfgifteConvector": "Kühlleistung Konvektor",
			"koudeAfgifteConvectorMetClimateBooster": "Kühlleistung Konvektor mit ClimateBooster",
			"koudeAfgifteConvectorMetClimateBoosterError": "ClimateBooster-Lüfter sind nicht aktiv. Die Lüfter werden aktiviert, wenn die Temperaturdifferenz zwischen Luft und Vorlaufwasser größer als 3 Grad ist.",
			"koudeAfgifteConvectorMetClimateBoosterHelp": "Geschätzte Kühlleistung des gegebenen Wand- oder freistehenden Konvektors mit einem Convector Pro.",
			"answer1Comment1": "Konvektoren werden üblicherweise in Standard- und 'Twin'-Ausführung geliefert. Ein Twin-Modell verwendet einen doppelten Wärmetauscher. Ein Twin-Modell bietet mehr Leistung im gleichen Gehäuse.",
			"answer1Comment2": "Hersteller Jaga bezeichnet Modelle mit einem einzelnen Wärmetauscher als Typ 10, 15 und 20. Twin-Modelle mit Typ 11, 16 oder 21",
			"answer1Comment3": "Bei Hersteller Betherma werden die Modelle mit doppeltem Wärmetauscher mit dem Zusatz Twin in der Typenbezeichnung gekennzeichnet.",
			"answer3Comment1": "Geben Sie hier die gewünschte Vorlauftemperatur ein.",
			"answer4Comment1": "Die Rücklauftemperatur liegt durchschnittlich 5 bis 10 Grad unter der Vorlauftemperatur. Wenn Sie die Rücklauftemperatur nicht kennen, empfehlen wir eine um 10 Grad niedrigere Temperatur als die Vorlauftemperatur einzugeben.",
			"answer5Comment1": "Geben Sie hier die gewünschte Raumtemperatur ein.",
			"answer6Comment1": "Geschätzte Kühlleistung des Wand- oder freistehenden Konvektors ohne Installation eines Convector Pro."
		}
	};

	function getText(key, lang = 'nl') {
		return translations[lang][key] || key;
	}

	function updateLanguage(lang) {
		document.querySelectorAll('[data-translate]').forEach(element => {
			const key = element.getAttribute('data-translate');
			element.textContent = getText(key, lang);
		});

		document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
			const key = element.getAttribute('data-translate-placeholder');
			element.placeholder = getText(key, lang);
		});

		document.querySelectorAll('option[data-translate]').forEach(element => {
			const key = element.getAttribute('data-translate');
			element.textContent = getText(key, lang);
		});

		document.querySelectorAll('[data-translate-value]').forEach(element => {
			const key = element.getAttribute('data-translate-value');
			element.value = getText(key, lang);
		});
	}

	// Initialize language on page load
	document.addEventListener('DOMContentLoaded', function() {
		updateLanguage('nl');
	});
</script>

    <script type="text/javascript" language="javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>

    <script language="JavaScript" type="text/javascript">
function getRadioVal(form, name) {
    var val;
    // get list of radio buttons with specified name
    var radios = form.elements[name];
    
    // loop through list of radio buttons
    for (var i=0, len=radios.length; i<len; i++) {
        if ( radios[i].checked ) { // radio checked?
            val = radios[i].value; // if so, hold its value in val
            break; // and break out of for loop
        }
    }
    return val; // return value of checked radio or undefined if none checked
} 
function toggleDiv(div, show) {
  var x = document.getElementById(div);
  if (show==1) {
    x.style.display = 'block';
  } else {
    x.style.display = 'none';
  }
}

function reken (form)
{
var typeconvector = getRadioVal( document.getElementById('form'), 'typeconvector' );
var diepte = form.diepte.value;
var breedte = form.breedte.value;
var Twaterin = form.Twaterin.value;
var Twaterout = form.Twaterout.value;
var Troom = form.Troom.value;
var deltaTwater = Twaterin/2+Twaterout/2;
var deltaT = Troom-deltaTwater;
var Qnominal = 0;
var Zconv = 0;
switch (typeconvector) {
    case 'enkel':
		switch (diepte) {
			case "d100":
				Zconv=60;
				BST=2.8;		
				break;
			case "d150":
				Zconv=75;
				BST=2.7;		
				break;
			case "d200":
				Zconv=90;
				BST=2.6;		
				break;
			case "d250":
				Zconv=105;
				BST=2.5;	
				break;
	        break;
		}
		break;
    case 'twin':
		switch (diepte) {
			case "d100":
				Zconv=75;
				BST=2.8;	
				break;
			case "d150":
				Zconv=94;
				BST=2.8;	
				break;
			case "d200":
				Zconv=113;
				BST=2.8;	
				break;
			case "d250":
				Zconv=132;
				BST=2.8;	
		        break;
			}
        break;
	break;
}
//form.Qnominal.value = Qnominal;
//var Qnominal = form.Qnominal.value;
var fractie= Math.pow(deltaT/8,1);
Qnominal=(breedte/1000*Zconv)*fractie;
QwithCB=Qnominal*BST;
form.Qnominal.value = Math.round(Qnominal)
if((Troom-Twaterin)<3){
	form.Q_outputCB.value = Math.round(Qnominal)
	toggleDiv('foutmelding', '1');
}else{
	form.Q_outputCB.value = Math.round(QwithCB);
	toggleDiv('foutmelding', '0');
}
}</script>
    <script language="JavaScript" type="text/javascript">
    $(document).ready(function(){
    $("#question-1").click(function(){
        $("#answer-1").toggle(1000);
    });
	$("#question-2").click(function(){
        $("#answer-2").toggle(1000);
    });
	$("#question-3").click(function(){
        $("#answer-3").toggle(1000);
    });
	$("#question-4").click(function(){
        $("#answer-4").toggle(1000);
    });
	$("#question-5").click(function(){
        $("#answer-5").toggle(1000);
    });
	$("#question-6").click(function(){
        $("#answer-6").toggle(1000);
    });
	$("#question-7").click(function(){
        $("#answer-7").toggle(1000);
    });
	$("#question-8").click(function(){
        $("#answer-8").toggle(1000);
    });
	$("#question-9").click(function(){
        $("#answer-9").toggle(1000);
    });
	$("#question-10").click(function(){
        $("#answer-10").toggle(1000);
    });
	$("#question-11").click(function(){
        $("#answer-11").toggle(1000);
    });
});</script>

<!-- Add language selector -->
<div style="text-align: right; margin-bottom: 20px;">
	<select id="languageSelector" onchange="updateLanguage(this.value)" style="padding: 8px; border-radius: 4px; border: 1px solid #ccc; max-width: 150px;">
		<option value="nl">Nederlands</option>
		<option value="en">English</option>
		<option value="de">Deutsch</option>
	</select>
</div>

<form name="form" class="form" id="form">
<table style="width:100%; background:#fff;">
	<colgroup> 
		<col style="width: 25% !important;"> 
		<col style="width: 5% !important;"> 
		<col style="width: 35% !important;"> 
		<col style="width: 25% !important;"> 
	</colgroup>
	<tbody><tr>
		<td colspan="4" class="table-head"><h3 data-translate="wandhangende">Gegevens wandhangende of vrijstaande convector</h3></td>
	</tr>
    <tr>
		<td data-translate="type">Type</td>
		<td>
        	<img id="question-1" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp">
        </td>
		<td>
        <div style="width:50%; float:left; text-align:center;">
        	<img src="https://static.wixstatic.com/media/159d65_fac526fe566140d6b27669fae097f789~mv2.jpg/v1/fill/w_305,h_320,al_c,q_80/enkel.webp" style="width:100%;">
	        <p><input type="radio" value="enkel" name="typeconvector" checked="checked"><span data-translate="standaard">standaard</span></p>
         </div>
        <div style="width:50%; float:left; text-align:center">
            <img src="https://static.wixstatic.com/media/159d65_13147ca3f81c45e49c30da156f9bd2f1~mv2.jpg/v1/fill/w_305,h_320,al_c,q_80/twin.webp" style="width:100%;">
            <p><input type="radio" value="twin" name="typeconvector"><span data-translate="twin">twin</span></p>
        </div>
        </td>
		<td>&nbsp;</td>
	</tr>
    <tr id="answer-1" class="answer">
    	<td colspan="4">
		   <p data-translate="answer1Comment1">Convectoren worden veelal geleverd in een standaard- en een 'twin'-model. Bij een twin-model wordt een dubbele warmtewisselaar toegepast. Een twin-model biedt meer afgifte in dezelfde omkasting. </p>
           <p data-translate="answer1Comment2">Farbikant Jaga duidt modellen met een enkele warmtewisselaar aan met type 10, 15 en 20. Twin modellen met type 11, 16 of 21</p>
           <p data-translate="answer1Comment3">Farbikant Betherma worden de modelen met dubbele warmtewisselaar aangeduid met toevoeging twin in het typenummer.</p>
	    </td>
    </tr>
    <tr>
		<td data-translate="diepteKast">diepte kast</td>
		<td>&nbsp;</td>
		<td>
		    <select name="diepte">
				<option value="d100" selected="selected">100</option>
				<option value="d150">150</option>
    	        <option value="d200">200</option>
				<option value="d250">250</option>
			</select> 
        </td>
		<td>mm</td>
	</tr>    
	<tr>
		<td data-translate="breedteKast">breedte kast</td>
		<td>&nbsp;</td>
		<td><input size="" name="breedte" value="1000"></td>
		<td>mm</td>
	</tr>
    <tr>
		<td colspan="4" class="table-head"><h3 data-translate="werktemperatuur">werktemperatuur</h3></td>
	</tr>
	<tr>
		<td data-translate="ingaandeWatertemperatuur">Ingaande watertemperatuur</td>
		<td><img id="question-3" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp"></td>
		<td><input size="5" name="Twaterin" value="17"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-3" class="answer">
    	<td colspan="4">
		    <p data-translate="answer3Comment1">Vul hier de gewenste ingaande watertemperatuur in.</p>
	    </td>
    </tr>
	<tr>
		<td data-translate="uitgaandeWatertemperatuur">Uitgaande watertemperatuur</td>
		<td><img id="question-4" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp"></td>
		<td><input size="5" name="Twaterout" value="19"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-4" class="answer">
    	<td colspan="4">
		    <p data-translate="answer4Comment1">De uitgaande watertemperatuur ligt gemiddeld 5 tot 10 graden 
lager dan de ingaande watertemperatuur. Weet u de uitgaande temperatuur niet,
dan raden wij u aan een 10 graden lagere temperatuur dan de ingaande temperatuur in te 
vullen.</p>
	    </td>
    </tr>
	<tr>
		<td data-translate="ruimtetemperatuur">Ruimtetemperatuur</td>
		<td><img id="question-5" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp"></td>
		<td><input size="5" name="Troom" value="28"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-5" class="answer">
    	<td colspan="4">
		    <p data-translate="answer5Comment1">Vul hier de gewenste ruimtetemperatuur in.</p>
	    </td>
    </tr>
	<tr>
		<td colspan="4" class="table-head"><h3 data-translate="resultaat">Resultaat</h3></td>
	</tr>

	<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>
			<input onClick="reken(this.form)" value="Bereken" data-translate-value="bereken" id="calculate" type="button">
		</td>
		<td>&nbsp;</td>
	</tr>
		<tr>
		<td data-translate="koudeAfgifteConvector">Koudeafgifte convector</td>
		<td>
        	<img id="question-6" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp">
        </td>
		<td><input size="5" name="Qnominal" value=""></td>
		<td>Watt</td>
	</tr>
    <tr id="answer-6" class="answer">
    	<td colspan="4">
        	<p data-translate="answer6Comment1">Geschatte koudeafgifte van de wandhangende en vrijstaande convector zonder plaatsing van een Convector Pro.</p>
        </td>
    </tr>
	<tr>
		<td data-translate="koudeAfgifteConvectorMetClimateBooster"><b>Koudeafgifte convector met ClimateBooster</b></td>
		<td><img src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_43,h_45,al_c,lg_1,q_80/logo-question-mark-184355.webp" alt="" class="question" id="question-8"></td>
		<td><strong>
		  <input size="5" name="Q_outputCB" value="">
		</strong>
		<div id="foutmelding"><p data-translate="koudeAfgifteConvectorMetClimateBoosterError">Ventilatoren ClimateBooster niet actief. Ventilatoren wordt geactiveerd wanneer het temperatuurverschil tussen lucht en het ingaande water groter is dan 3 graden.</p></div></td>
		<td><strong>Watt</strong></td>
	</tr>
    <tr id="answer-8" class="answer">
    	<td colspan="4">
        	<p data-translate="koudeAfgifteConvectorMetClimateBoosterHelp">Geschatte koudeafgifte van de ingegeven wandhangende en vrijstaande convector welke voorzien is van een Convector Pro.</p>
        </td>
    </tr>
</tbody></table>
</form>
