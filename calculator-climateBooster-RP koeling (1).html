﻿
<style type="text/css">
	:root {
		--primary-color: #8dc63f;
		--background-color: #ffffff;
		--text-color: #333333;
		--border-radius: 8px;
		--shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		--transition: all 0.3s ease;
	}

	html, body {
		background-color: transparent !important;
		margin: 0;
		padding: 0;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
		color: var(--text-color);
		line-height: 1.6;
	}

	body {
		padding: 20px;
		font-size: 16px;
	}

	/* Modern table styles */
	table {
		width: 100%;
		max-width: 100%;
		margin-bottom: 1.5rem;
		border-collapse: separate;
		border-spacing: 0;
		background: var(--background-color);
		border-radius: var(--border-radius);
		box-shadow: var(--shadow);
	}

	td {
		padding: 12px 16px;
		transition: var(--transition);
	}

	tr:hover {
		background-color: rgba(0, 0, 0, 0.02);
	}

	/* Modern form elements */
	input, select {
		width: 100%;
		max-width: 300px;
		padding: 12px;
		margin: 5px 0;
		border: 1px solid #ddd;
		border-radius: var(--border-radius);
		box-sizing: border-box;
		transition: var(--transition);
		outline: none;
	}

	input:focus, select:focus {
		border-color: var(--primary-color);
		box-shadow: 0 0 0 2px rgba(141, 198, 63, 0.1);
	}

	input#calculate {
		background-color: var(--primary-color);
		color: white;
		font-weight: 600;
		border: none;
		padding: 14px 24px;
		cursor: pointer;
		transition: var(--transition);
	}

	input#calculate:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 8px rgba(141, 198, 63, 0.2);
	}

	input#calculate:active {
		transform: translateY(0);
	}

	/* Question icon and answers */
	.question {
		cursor: pointer;
		width: 24px;
		height: 24px;
		transition: var(--transition);
	}

	.question:hover {
		transform: scale(1.1);
	}

	[id^="answer-"] {
		display: none;
		background: #f8f9fa;
		padding: 20px;
		margin: 10px 0;
		border-radius: var(--border-radius);
		box-shadow: var(--shadow);
	}

	.answer p {
		font-size: 14px;
		margin: 0 5%;
		color: #666;
	}

	.table-head {
		background: #f1f3f5;
		border-radius: var(--border-radius) var(--border-radius) 0 0;
	}

	.table-head h3 {
		margin: 0;
		padding: 15px;
		color: var(--text-color);
	}

	/* Media Queries */
	@media screen and (max-width: 768px) {
		body {
			padding: 15px;
		}

		.answer p {
			margin: 0 2%;
		}

		td {
			padding: 10px 12px;
		}
	}

	@media screen and (max-width: 480px) {
		body {
			padding: 10px;
			font-size: 14px;
		}

		table {
			display: block;
			overflow-x: auto;
			-webkit-overflow-scrolling: touch;
		}

		input, select {
			font-size: 16px;
			padding: 10px;
		}

		.answer p {
			margin: 0;
			font-size: 13px;
		}

		td {
			padding: 8px 10px;
		}
	}
</style>

<!-- Add translations -->
<script type="text/javascript">
	const translations = {
		nl: {
			"gegevensRadiator": "Gegevens radiator",
			"type": "Type",
			"hoogte": "Hoogte",
			"breedte": "Breedte",
			"watertemperatuurAanvoer": "Watertemperatuur aanvoer",
			"watertemperatuurRetour": "Watertemperatuur retour",
			"ruimtetemperatuur": "Ruimtetemperatuur",
			"werktemperatuur": "Werktemperatuur",
			"bereken": "Bereken",
			"resultaat": "Resultaat",
			"vermogenZonderCB": "Vermogen zonder ClimateBooster",
			"vermogenMetCB": "Vermogen met ClimateBooster",
			"nominalVermogen": "Nominaal vermogen",
			"selecteerType": "Selecteer type",
			"selecteerHoogte": "Selecteer hoogte",
			"voerBreedteIn": "Voer breedte in",
			"h300": "300mm",
			"h400": "400mm",
			"h500": "500mm",
			"h600": "600mm",
			"h700": "700mm",
			"h900": "900mm",
			// Help texts
			"helpWaterTempRetourVerwarming": "De uitgaande watertemperatuur ligt gemiddeld 5 tot 10 graden lager dan de ingaande temperatuur. Weet u de uitgaande temperatuur niet, dan raden wij u aan een 10 graden lagere temperatuur dan de ingaande temperatuur in te vullen.",
			"helpWaterTempRetourKoeling": "De uitgaande watertemperatuur ligt gemiddeld 2 tot 3 graden hoger dan de ingaande temperatuur. Weet u de uitgaande temperatuur niet, dan raden wij u aan een 3 graden hogere temperauur dan de ingaande temperatuur in te vullen.",
			"helpRuimtetemperatuur": "Vul hier de gewenste ruimtetemperatuur in.",
			"helpwatertemperatuurAanvoer": "Vul hier de gewenste ingaande watertemperatuur in.",
			"helpConvectorType": "Convectoren worden veelal geleverd in een 'standaard' of een 'twin' model. Bij een twin model wordt een dubbele warmtewisselaar toegepast. Een twin model biedt meer warmteafgifte in dezelfde omkasting. Fabrikant Jaga duidt modellen met een enkele warmtewisselaar aan met type 10, 15 en 20. Twin modellen met type 11, 16 of 21. Bij fabrikant Betherma worden de modelen met een dubbele warmtewisselaar aangeduid met de toevoeging 'twin' in het typenummer.",
			"coldOutputWithoutClimateBooster": "Koudeafgifte radiator zonder ClimateBooster",
			"coldOutputWithClimateBooster": "Koudeafgifte radiator met ClimateBooster",
			"coldOutputWithClimateBoosterHelp": "Koudeafgifte van de radiator bij de ingegeven temperaturen op basis van metingen aan diverse radiatoren.",
			"coldOutputWithoutClimateBoosterHelp": "Geschatte koudeafgifte van de ingegeven radiator bij de gewenste watertemperatuur zonder plaatsing van een ClimateBooster.",
			"coldOutputWithClimateBoosterError": "Ventilatoren ClimateBooster niet actief. Ventilatoren wordt geactiveerd als het temperatuurverschil tussen lucht en ingaand water groter dan 3 graden is.",
			"helpNominalPower": "Geschatte warmteafgifte van de radiator bij 75/65/20. Voor exacte warmteafgifte raadpleeg website fabrikant.",
			"helpConvectorType1": "De zijde van de radiator waar het water doorheen stroomt, is een plaat. Deze plaat kan worden voorzien van convectoren, aan de binnen- of achterkant. Door de afmetingen, hoeveelheid en convectoren wordt de capaciteit bepaald in Watt",
			"helpConvectorType2": "Een type 10 radiator heeft 1 plaat maar geen convector.",
			"helpConvectorType3": "Een type 11 radiator heeft 1 plaat en 1 convector.",
			"helpConvectorType4": "Een type 20 radiator heeft 2 platen maar geen convector.",
			"helpConvectorType5": "Een type 21 radiator heeft 2 platen en 1 convector.",
			"helpConvectorType6": "Een type 22 radiator heeft 2 platen en 2 convectoren.",
			"helpConvectorType7": "Een type 30 radiator heeft 3 platen en geen convectoren.",
			"helpConvectorType8": "Een type 33 radiator heeft 3 platen en 3 convectoren."
		},
		en: {
			"gegevensRadiator": "Radiator details",
			"type": "Type",
			"hoogte": "Height",
			"breedte": "Width",
			"watertemperatuurAanvoer": "Water supply temperature",
			"watertemperatuurRetour": "Water return temperature",
			"ruimtetemperatuur": "Room temperature",
			"werktemperatuur": "Working temperature",
			"bereken": "Calculate",
			"resultaat": "Result",
			"vermogenZonderCB": "Power without ClimateBooster",
			"vermogenMetCB": "Power with ClimateBooster",
			"nominalVermogen": "Nominal power",
			"selecteerType": "Select type",
			"selecteerHoogte": "Select height",
			"voerBreedteIn": "Enter width",
			"h300": "300mm",
			"h400": "400mm",
			"h500": "500mm",
			"h600": "600mm",
			"h700": "700mm",
			"h900": "900mm",
			// Help texts
			"helpWaterTempRetourVerwarming": "The outgoing water temperature is on average 5 to 10 degrees lower than the incoming temperature. If you don't know the outgoing temperature, we recommend entering a temperature 10 degrees lower than the incoming temperature.",
			"helpWaterTempRetourKoeling": "The outgoing water temperature is on average 2 to 3 degrees higher than the incoming temperature. If you don't know the outgoing temperature, we recommend entering a temperature 3 degrees higher than the incoming temperature.",
			"helpRuimtetemperatuur": "Enter the desired room temperature here.",
			"helpwatertemperatuurAanvoer": "Enter the desired incoming water temperature here.",
			"helpConvectorType": "Convectors are usually supplied in a 'standard' or 'twin' model. A twin model uses a double heat exchanger. A twin model offers more heat output in the same casing. Manufacturer Jaga designates models with a single heat exchanger as type 10, 15 and 20. Twin models with type 11, 16 or 21. At manufacturer Betherma, the models with a double heat exchanger are indicated with the addition 'twin' in the type number.",
			"coldOutputWithoutClimateBooster": "Cold output radiator without ClimateBooster",
			"coldOutputWithClimateBooster": "Cold output radiator with ClimateBooster",
			"coldOutputWithClimateBoosterHelp": "Estimated cold output of the radiator at the given temperatures based on measurements on various radiators.",
			"coldOutputWithoutClimateBoosterHelp": "Estimated cold output of the entered radiator at the desired water temperature without installation of a ClimateBooster.",
			"coldOutputWithClimateBoosterError": "Ventilators ClimateBooster not active. Ventilators is activated when the temperature difference between air and incoming water is greater than 3 degrees.",
			"helpNominalPower": "Estimated heat output of the radiator at 75/65/20. For exact heat output, please refer to the manufacturer's website.",
			"helpConvectorType1": "The side of the radiator where the water flows through is a plate. This plate can be fitted with convectors on the inside or back. The capacity in Watts is determined by the dimensions, quantity and convectors",
			"helpConvectorType2": "A type 10 radiator has 1 plate but no convector.",
			"helpConvectorType3": "A type 11 radiator has 1 plate and 1 convector.",
			"helpConvectorType4": "A type 20 radiator has 2 plates but no convector.",
			"helpConvectorType5": "A type 21 radiator has 2 plates and 1 convector.",
			"helpConvectorType6": "A type 22 radiator has 2 plates and 2 convectors.",
			"helpConvectorType7": "A type 30 radiator has 3 plates and no convectors.",
			"helpConvectorType8": "A type 33 radiator has 3 plates and 3 convectors."
		},
		de: {
			"gegevensRadiator": "Heizkörper Details",
			"type": "Typ",
			"hoogte": "Höhe",
			"breedte": "Breite",
			"watertemperatuurAanvoer": "Wasservorlauftemperatur",
			"watertemperatuurRetour": "Wasserrücklauftemperatur",
			"ruimtetemperatuur": "Raumtemperatur",
			"werktemperatuur": "Arbeitstemperatur",
			"bereken": "Berechnen",
			"resultaat": "Ergebnis",
			"vermogenZonderCB": "Leistung ohne ClimateBooster",
			"vermogenMetCB": "Leistung mit ClimateBooster",
			"nominalVermogen": "Nennleistung",
			"selecteerType": "Typ auswählen",
			"selecteerHoogte": "Höhe auswählen",
			"voerBreedteIn": "Breite eingeben",
			"h300": "300mm",
			"h400": "400mm",
			"h500": "500mm",
			"h600": "600mm",
			"h700": "700mm",
			"h900": "900mm",
			// Help texts
			"helpWaterTempRetourVerwarming": "Die Wasserauslauftemperatur liegt durchschnittlich 5 bis 10 Grad unter der Einlauftemperatur. Wenn Sie die Auslauftemperatur nicht kennen, empfehlen wir eine um 10 Grad niedrigere Temperatur als die Einlauftemperatur einzugeben.",
			"helpWaterTempRetourKoeling": "Die Wasserauslauftemperatur liegt durchschnittlich 2 bis 3 Grad über der Einlauftemperatur. Wenn Sie die Auslauftemperatur nicht kennen, empfehlen wir eine um 3 Grad höhere Temperatur als die Einlauftemperatur einzugeben.",
			"helpRuimtetemperatuur": "Geben Sie hier die gewünschte Raumtemperatur ein.",
			"helpwatertemperatuurAanvoer": "Geben Sie hier die gewünschte Wasservorlauftemperatur ein.",
			"helpConvectorType": "Convectoren werden vielal geleverd in einem 'standart' oder 'twin' Modell. Bei einem twin Modell wird ein doppelter Wärmetauscher angewendet. Ein twin Modell bietet mehr Wärmeabgabe in der gleichen Gehäusegröße. Hersteller Jaga bezeichnet Modelle mit einem einzigen Wärmetauscher als Typ 10, 15 und 20. Twin Modelle mit Typ 11, 16 oder 21. Bei Hersteller Betherma werden die Modelle mit einem doppelter Wärmetauscher mit der Zusatzbezeichnung 'twin' in der Modellnummer angegeben.",
			"coldOutputWithoutClimateBooster": "Kälteausgabe Heizkörper ohne ClimateBooster",
			"coldOutputWithClimateBooster": "Kälteausgabe Heizkörper mit ClimateBooster",
			"coldOutputWithClimateBoosterHelp": "Geschätzte Kälteausgabe des Heizkörpers bei den eingegebenen Temperaturen basierend auf Messungen an verschiedenen Heizkörpern.",
			"coldOutputWithoutClimateBoosterHelp": "Geschätzte Kälteausgabe des Heizkörpers bei der gewünschten Wassertemperatur ohne Installation eines ClimateBooster.",
			"coldOutputWithClimateBoosterError": "Ventilatoren ClimateBooster nicht aktiv. Ventilatoren wird geactiveerd als das Temperaturunterschied zwischen Luft und eingehendem Wasser größer als 3 Grad ist.",
			"helpNominalPower": "Geschätzte Wärmeabgabe des Heizkörpers bei 75/65/20. Für genaue Wärmeabgabe bitte auf der Fabrikanten-Website nachschauen.",
			"helpConvectorType1": "Die Seite des Heizkörpers, durch die das Wasser fließt, ist eine Platte. Diese Platte kann mit Konvektoren an der Innen- oder Rückseite ausgestattet werden. Die Leistung in Watt wird durch die Abmessungen, Anzahl und Konvektoren bestimmt",
			"helpConvectorType2": "Ein Typ 10 Heizkörper hat 1 Platte aber keinen Konvektor.",
			"helpConvectorType3": "Ein Typ 11 Heizkörper hat 1 Platte und 1 Konvektor.",
			"helpConvectorType4": "Ein Typ 20 Heizkörper hat 2 Platten aber keinen Konvektor.",
			"helpConvectorType5": "Ein Typ 21 Heizkörper hat 2 Platten und 1 Konvektor.",
			"helpConvectorType6": "Ein Typ 22 Heizkörper hat 2 Platten und 2 Konvektoren.",
			"helpConvectorType7": "Ein Typ 30 Heizkörper hat 3 Platten und keine Konvektoren.",
			"helpConvectorType8": "Ein Typ 33 Heizkörper hat 3 Platten und 3 Konvektoren."
		}
	};

	function getText(key, lang = 'nl') {
		return translations[lang][key] || key;
	}

	function updateLanguage(lang) {
		document.querySelectorAll('[data-translate]').forEach(element => {
			const key = element.getAttribute('data-translate');
			element.textContent = getText(key, lang);
		});

		document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
			const key = element.getAttribute('data-translate-placeholder');
			element.placeholder = getText(key, lang);
		});

		document.querySelectorAll('option[data-translate]').forEach(element => {
			const key = element.getAttribute('data-translate');
			element.textContent = getText(key, lang);
		});

		document.querySelectorAll('[data-translate-value]').forEach(element => {
			const key = element.getAttribute('data-translate-value');
			element.value = getText(key, lang);
		});
	}

	// Initialize language on page load
	document.addEventListener('DOMContentLoaded', function() {
		updateLanguage('nl');
	});
</script>

    <script type="text/javascript" language="javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>

    <script language="JavaScript" type="text/javascript">
    function getRadioVal(form, name) {
    var val;
    // get list of radio buttons with specified name
    var radios = form.elements[name];
    
    // loop through list of radio buttons
    for (var i=0, len=radios.length; i<len; i++) {
        if ( radios[i].checked ) { // radio checked?
            val = radios[i].value; // if so, hold its value in val
            break; // and break out of for loop
        }
    }
    return val; // return value of checked radio or undefined if none checked
} 
function toggleDiv(div, show) {
  var x = document.getElementById(div);
  if (show==1) {
    x.style.display = 'block';
  } else {
    x.style.display = 'none';
  }
}

function reken (form)
{
var typerad = getRadioVal( document.getElementById('form'), 'typerad' );
var hoogte = form.hoogte.value;
var breedte = form.breedte.value;
var Twaterin = form.Twaterin.value;
var Twaterout = form.Twaterout.value;
var Troom = form.Troom.value;
var deltaTwater = Twaterin/2+Twaterout/2;
var deltaT = Troom-deltaTwater;
var Zrad = 0;
var BST =0;
var Qnominal=0;
var QwithCB=0;
switch (typerad) {
    case "t20":
	Zrad=160;
	BST=2.2;
		break;
    case "t21":
	Zrad=180;
	BST=2.2;
        break;
    case "t22":
	Zrad=200;
	BST=2.2;
		break;
    case "t30":
	Zrad=240;
	BST=1.85;
		break;
    case "t33":
		Zrad=300;
		BST=1.85;
		break;  
}
var fractie= Math.pow(deltaT/10,0.8);
Qnominal=(hoogte/1000*breedte/1000*Zrad)*fractie;
QwithCB=Qnominal*BST;
form.Qnominal.value = Math.round(Qnominal)
if((Troom-Twaterin)<3){
	form.Q_outputCB.value = Math.round(Qnominal)
	toggleDiv('foutmelding', '1');
}else{
	form.Q_outputCB.value = Math.round(QwithCB);
	toggleDiv('foutmelding', '0');
}
}
</script>
    <script language="JavaScript" type="text/javascript">
    $(document).ready(function(){
    $("#question-1").click(function(){
        $("#answer-1").toggle(1000);
    });
	$("#question-2").click(function(){
        $("#answer-2").toggle(1000);
    });
	$("#question-3").click(function(){
        $("#answer-3").toggle(1000);
    });
	$("#question-4").click(function(){
        $("#answer-4").toggle(1000);
    });
	$("#question-5").click(function(){
        $("#answer-5").toggle(1000);
    });
	$("#question-6").click(function(){
        $("#answer-6").toggle(1000);
    });
	$("#question-7").click(function(){
        $("#answer-7").toggle(1000);
    });
	$("#question-8").click(function(){
        $("#answer-8").toggle(1000);
    });
	$("#question-9").click(function(){
        $("#answer-9").toggle(1000);
    });
	$("#question-10").click(function(){
        $("#answer-10").toggle(1000);
    });
	$("#question-11").click(function(){
        $("#answer-11").toggle(1000);
    });
});</script>

<!-- Add language selector -->
<div style="text-align: right; margin-bottom: 20px;">
	<select id="languageSelector" onchange="updateLanguage(this.value)" style="padding: 8px; border-radius: 4px; border: 1px solid #ccc; max-width: 150px;">
		<option value="nl">Nederlands</option>
		<option value="en">English</option>
		<option value="de">Deutsch</option>
	</select>
</div>

<form name="form" class="form" id="form">
<table style="width:100%; background:#fff;">
	<colgroup> 
		<col style="width: 25% !important;"> 
		<col style="width: 5% !important;"> 
		<col style="width: 35% !important;"> 
		<col style="width: 25% !important;"> 
	</colgroup>
	<tbody><tr>
		<td colspan="4" class="table-head"><h3 data-translate="gegevensRadiator">Gegevens radiator</h3></td>
	</tr>
    <tr>
		<td data-translate="type">Type</td>
		<td>
        	<img id="question-1" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_32,h_32,al_c/159d65_9e557acf4562457b870fac098f6c7132~mv2.png">
        </td>
		<td>
        <div style="width:20%; float:left; text-align:center;">
        	<img src="https://static.wixstatic.com/media/159d65_dcbc172036a3491ca43089634fe79108~mv2.png/v1/fill/w_34,h_132,al_c/159d65_dcbc172036a3491ca43089634fe79108~mv2.png">
	        <p><input type="radio" value="t20" name="typerad" >20</p>
         </div>
        <div style="width:20%; float:left; text-align:center">
            <img src="https://static.wixstatic.com/media/159d65_53c2bbb32c8d4585b6ac41e17c2be131~mv2.png/v1/fill/w_33,h_132,al_c/159d65_53c2bbb32c8d4585b6ac41e17c2be131~mv2.png">
            <p><input type="radio" value="t21" name="typerad">21</p>
        </div>
        <div style="width:20%; float:left; text-align:center">
        	<img src="https://static.wixstatic.com/media/159d65_926f211c0672498cb7be4969e66af139~mv2.png/v1/fill/w_42,h_132,al_c/159d65_926f211c0672498cb7be4969e66af139~mv2.png">
        	<p><input type="radio" value="t22" name="typerad" checked>22</p>
        </div>
		 <div style="width:20%; float:left; text-align:center">
        	<img src="https://static.wixstatic.com/media/159d65_9bdac7bb140d43cf9816e2d462da4554~mv2.png/v1/fill/w_63,h_132,al_c/159d65_9bdac7bb140d43cf9816e2d462da4554~mv2.png">
        	<p><input type="radio" value="t30" name="typerad">30</p>
        </div>
        <div style="width:20%; float:left; text-align:center">
        	<img src="https://static.wixstatic.com/media/159d65_2339d345c7604f9987503541f717e5eb~mv2.png/v1/fill/w_63,h_132,al_c/159d65_2339d345c7604f9987503541f717e5eb~mv2.png">
        	<p><input type="radio" value="t33" name="typerad">33</p>
        </div>
        </td>
		<td>&nbsp;</td>
	</tr>
    <tr id="answer-1" class="answer">
    	<td colspan="4">
			<p data-translate="helpConvectorType1">De zijde van de radiator waar het water doorheen stroomt, is een plaat.
			 Deze plaat kan worden voorzien van convectoren, aan de binnen- of achterkant.
			 Door de afmetingen, hoeveelheid en convectoren wordt de capaciteit bepaald in Watt</p>
	 
			 <p data-translate="helpConvectorType2">Een type 10 radiator heeft 1 plaat maar geen convector.</p>
			 <p data-translate="helpConvectorType3">Een type 11 radiator heeft 1 plaat en 1 convector.</p>
			 <p data-translate="helpConvectorType4">Een type 20 radiator heeft 2 platen maar geen convector.</p>
			 <p data-translate="helpConvectorType5">Een type 21 radiator heeft 2 platen en 1 convector.</p>
			 <p data-translate="helpConvectorType6">Een type 22 radiator heeft 2 platen en 2 convectoren.</p>
			 <p data-translate="helpConvectorType7">Een type 30 radiator heeft 3 platen en geen convectoren.</p>
			 <p data-translate="helpConvectorType8">Een type 33 radiator heeft 3 platen en 3 convectoren.</p>
		 </td>
    </tr>
    <tr>
		<td data-translate="hoogte">Hoogte</td>
		<td>&nbsp;</td>
		<td>
		    <select name="hoogte">
				<option value="300">300</option>
				<option value="400">400</option>
    	        <option value="500">500</option>
				<option value="600">600</option>
        	   	<option value="700">700</option>
                <option value="900">900</option>
			</select> 
        </td>
		<td>mm</td>
	</tr>    
	<tr>
		<td data-translate="breedte">Breedte</td>
		<td>&nbsp;</td>
		<td><input size="" name="breedte" value="1000"></td>
		<td>mm</td>
	</tr>
    <tr>
		<td colspan="4" class="table-head"><h3 data-translate="werktemperatuur">Werktemperatuur</h3></td>
	</tr>
	<tr>
		<td data-translate="watertemperatuurAanvoer">Ingaande watertemperatuur</td>
		<td><img id="question-3" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_32,h_32,al_c/159d65_9e557acf4562457b870fac098f6c7132~mv2.png"></td>
		<td><input size="5" name="Twaterin" value="17"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-3" class="answer">
    	<td colspan="4">
		    <p data-translate="helpwatertemperatuurAanvoer">Vul hier de gewenste ingaande watertemperatuur in.</p>
	    </td>
    </tr>
	<tr>
		<td data-translate="watertemperatuurRetour">Uitgaande watertemperatuur</td>
		<td><img id="question-4" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_32,h_32,al_c/159d65_9e557acf4562457b870fac098f6c7132~mv2.png"></td>
		<td><input size="5" name="Twaterout" value="19"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-4" class="answer">
    	<td colspan="4">
		    <p data-translate="helpWaterTempRetourKoeling">De uitgaande watertemperatuur ligt gemiddeld 2 tot 3 graden hoger dan de ingaande temperatuur. Weet u de uitgaande temperatuur niet, dan raden wij u aan een 3 graden hogere temperauur dan de ingaande temperatuur in te vullen.</p>
	    </td>
    </tr>
	<tr>
		<td data-translate="ruimtetemperatuur">Ruimtetemperatuur</td>
		<td><img id="question-5" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_32,h_32,al_c/159d65_9e557acf4562457b870fac098f6c7132~mv2.png"></td>
		<td><input size="5" name="Troom" value="28"></td>
		<td>°C</td>
	</tr>
    <tr id="answer-5" class="answer">
    	<td colspan="4">
		    <p data-translate="helpRuimtetemperatuur">Vul hier de gewenste ruimtetemperatuur in.</p>
	    </td>
    </tr>
	<tr>
		<td colspan="4" class="table-head"><h3 data-translate="resultaat">Resultaat</h3></td>
	</tr>

	<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>
			<input onClick="reken(this.form)" data-translate-value="bereken" value="Bereken" id="calculate" type="button">
		</td>
		<td>&nbsp;</td>
	</tr>
		<tr>
			<td data-translate="coldOutputWithoutClimateBooster">Koudeafgifte radiator zonder ClimateBooster</td>
		<td>
        	<img id="question-6" class="question" src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_32,h_32,al_c/159d65_9e557acf4562457b870fac098f6c7132~mv2.png">
        </td>
		<td><input size="5" name="Qnominal" value=""></td>
		<td>Watt</td>
	</tr>
    <tr id="answer-6" class="answer">
    	<td colspan="4">
        	<p data-translate="coldOutputWithoutClimateBoosterHelp">Koudeafgifte van de radiator bij de ingegeven temperaturen op basis van metingen aan diverse radiatoren.</p>
        </td>
    </tr>
	<tr>
		<td><b data-translate="coldOutputWithClimateBooster">Koudeafgifte radiator met ClimateBooster</b></td>
		<td><img src="https://static.wixstatic.com/media/159d65_9e557acf4562457b870fac098f6c7132~mv2.png/v1/fill/w_32,h_32,al_c/159d65_9e557acf4562457b870fac098f6c7132~mv2.png" alt="" class="question" id="question-8"></td>
		<td><strong>
		  <input size="5" name="Q_outputCB" value="">
		  </strong>
		  <div id="foutmelding"><p data-translate="coldOutputWithClimateBoosterError">Ventilatoren ClimateBooster niet actief. Ventilatoren wordt geactiveerd als het temperatuurverschil tussen lucht en ingaand water groter dan 3 graden is.</p></div>
		</td>
		<td><strong>Watt</strong></td>
	</tr>
    <tr id="answer-8" class="answer">
    	<td colspan="4">
        	<p data-translate="coldOutputWithClimateBoosterHelp">Koudeafgifte van de radiator ingegeven radiator welke voorzien wordt van een ClimateBooster.</p>
        </td>
    </tr>
</tbody></table>
</form>
